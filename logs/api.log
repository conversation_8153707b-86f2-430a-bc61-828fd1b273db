2025-09-14 11:19:27 - api - INFO - SmartAPI client initialized successfully
2025-09-14 11:19:27 - api - ERROR - No instrument files found
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - api - ERROR - Error finding instrument: 'SEM_CUSTOM_SYMBOL'
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - api - ERROR - Error finding instrument: 'SEM_CUSTOM_SYMBOL'
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - api - ERROR - Error finding instrument: 'SEM_CUSTOM_SYMBOL'
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - api - ERROR - Error finding instrument: 'SEM_CUSTOM_SYMBOL'
2025-09-14 11:19:32 - api - INFO - SmartAPI client initialized successfully
2025-09-14 11:19:32 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - api - WARNING - No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.0, stop_loss=145.0, entry_price=150.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:32 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - api - WARNING - No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.0, stop_loss=145.0, entry_price=150.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:33 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - api - WARNING - No instrument found for signal: TradeSignal(symbol='NIFTY', action='TEMP', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 11, 19, 26, 916285), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type='TEMP', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:33 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - api - WARNING - No instrument found for signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 30, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=158.5, stop_loss=None, entry_price=None, exit_price=158.5, current_price=None, exit_reason='test_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:33 - api - INFO - Looking for instrument with custom symbol: NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - api - WARNING - No instrument found for signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 30, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=158.5, stop_loss=None, entry_price=None, exit_price=158.5, current_price=None, exit_reason='test_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:22:25 - api - INFO - SmartAPI client initialized successfully
2025-09-14 11:22:26 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:22:26 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:30:22 - api - INFO - SmartAPI client initialized successfully
2025-09-14 11:30:22 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:30:22 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:30:22 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 11:30:22 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 11:30:23 - api - ERROR - Trade execution failed: 'NoneType' object has no attribute 'get'
2025-09-14 11:30:23 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 11:30:23 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 11:32:41 - api - INFO - SmartAPI client initialized successfully
2025-09-14 11:32:41 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:32:41 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 11:32:41 - api - INFO - Looking for instrument with trading symbol: BANKNIFTY18SEP20255200000CE
2025-09-14 11:32:41 - api - INFO - Looking for instrument with trading symbol: BANKNIFTY18SEP20255200000CE
2025-09-14 11:32:42 - api - ERROR - Trade execution failed: 'NoneType' object has no attribute 'get'
2025-09-14 11:32:42 - api - INFO - Looking for instrument with trading symbol: BANKNIFTY18SEP20255200000CE
2025-09-14 11:32:42 - api - INFO - Looking for instrument with trading symbol: BANKNIFTY18SEP20255200000CE
