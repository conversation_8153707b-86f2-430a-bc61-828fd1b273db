2025-09-14 11:05:15 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:05:15 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:05:15 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:05:16 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 11:05:44 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:05:44 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:05:44 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:05:44 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
