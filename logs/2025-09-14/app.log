2025-09-14 11:04:19 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:04:20 - main - INFO - ✅ <PERSON><PERSON> started as <PERSON><PERSON><PERSON><PERSON>_S_R
2025-09-14 11:04:20 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:04:20 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:04:20 - main - INFO - 🔄 Listening for messages...
[E 250914 11:05:16 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '0f:a3:44:84:ed:39', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'tradingsymbol': 'NIFTY-Sep2025-25000-CE', 'symboltoken': '44668'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
2025-09-14 11:05:16 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Unable to fetch market data
[E 250914 11:05:44 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '0f:a3:44:84:ed:39', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'tradingsymbol': 'NIFTY-Sep2025-25000-CE', 'symboltoken': '44668'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
2025-09-14 11:05:44 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: Unable to fetch market data
2025-09-14 11:15:22 - main - INFO - 🛑 Bot stopped by user
2025-09-14 11:25:21 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:25:27 - main - ERROR - ❌ Bot crashed: database is locked
[E 250914 11:30:23 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder. Error: Quantity is invalid. It should be in multiples of lot size.. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '91:fe:7c:93:25:2f', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'variety': 'NORMAL', 'tradingsymbol': 'NIFTY16SEP2525000CE', 'symboltoken': '44668', 'transactiontype': 'BUY', 'exchange': 'NFO', 'ordertype': 'MARKET', 'producttype': 'INTRADAY', 'duration': 'DAY', 'price': '0', 'squareoff': '0', 'stoploss': '0', 'quantity': '1'}, Response: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
[E 250914 11:30:23 smartConnect:336] API request failed: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
[E 250914 11:32:42 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder. Error: Quantity is invalid. It should be in multiples of lot size.. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '33:1d:8f:b4:3e:1f', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'variety': 'NORMAL', 'tradingsymbol': 'BANKNIFTY28OCT2552000CE', 'symboltoken': '52954', 'transactiontype': 'BUY', 'exchange': 'NFO', 'ordertype': 'MARKET', 'producttype': 'INTRADAY', 'duration': 'DAY', 'price': '0', 'squareoff': '0', 'stoploss': '0', 'quantity': '1'}, Response: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
[E 250914 11:32:42 smartConnect:336] API request failed: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
2025-09-14 11:34:10 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:34:15 - main - ERROR - ❌ Bot crashed: database is locked
2025-09-14 11:34:22 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:34:28 - main - ERROR - ❌ Bot crashed: database is locked
2025-09-14 11:36:19 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:36:19 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 11:36:19 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:36:20 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:36:20 - main - INFO - 🔄 Listening for messages...
[E 250914 11:36:32 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder. Error: Quantity is invalid. It should be in multiples of lot size.. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/placeOrder, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'f9:f9:af:23:3b:67', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'variety': 'NORMAL', 'tradingsymbol': 'NIFTY16SEP2525000CE', 'symboltoken': '44668', 'transactiontype': 'BUY', 'exchange': 'NFO', 'ordertype': 'MARKET', 'producttype': 'INTRADAY', 'duration': 'DAY', 'price': '0', 'squareoff': '0', 'stoploss': '0', 'quantity': '1'}, Response: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
[E 250914 11:36:32 smartConnect:336] API request failed: {'message': 'Quantity is invalid. It should be in multiples of lot size.', 'errorcode': 'AB4014', 'status': False, 'data': None}
2025-09-14 11:36:32 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: 'NoneType' object has no attribute 'get'
2025-09-14 11:36:37 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: Invalid action CLOSE
2025-09-14 11:44:00 - main - INFO - 🛑 Bot stopped by user
2025-09-14 11:44:14 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:44:14 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 11:44:14 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:44:15 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:44:15 - main - INFO - 🔄 Listening for messages...
2025-09-14 11:44:36 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Trade execution error: 'str' object has no attribute 'get'
2025-09-14 11:45:08 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: No active position found for NIFTY
2025-09-14 11:46:30 - main - INFO - 🛑 Bot stopped by user
2025-09-14 11:46:47 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:46:47 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 11:46:47 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:46:47 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:46:47 - main - INFO - 🔄 Listening for messages...
2025-09-14 11:47:29 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Trade execution error: 'str' object has no attribute 'get'
2025-09-14 11:47:38 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: No active position found for NIFTY
2025-09-14 11:49:17 - main - INFO - 🛑 Bot stopped by user
2025-09-14 11:50:05 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:50:05 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 11:50:05 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:50:06 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:50:06 - main - INFO - 🔄 Listening for messages...
2025-09-14 11:50:18 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Unexpected response format: 09142e140e87AO
2025-09-14 11:50:22 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: No active position found for NIFTY
2025-09-14 11:54:35 - main - INFO - 🛑 Bot stopped by user
2025-09-14 11:55:05 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 11:55:05 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 11:55:05 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 11:55:06 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 11:55:06 - main - INFO - 🔄 Listening for messages...
2025-09-14 11:55:17 - main - ERROR - ❌ Invalid signal format. Supported formats:
1. ===Algo_Trading===$TRADE$BUY$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$STOP_LOSS$TIMESTAMP
2. ===Algo_Trading===$INTIMATION$Continue to hold...
3. ===Algo_Trading===$TRADE$CLOSE$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$TIMESTAMP$REASON
4. ===Algo_Trading===$Update$STOP LOSS to$PRICE$for option$SYMBOL...
5. Simple format: 'ACTION SYMBOL [STRIKE] [CE/PE]'
2025-09-14 11:55:42 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Order placed successfully: BUY 75 units
2025-09-14 11:56:22 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: 'str' object has no attribute 'get'
2025-09-14 11:58:11 - main - INFO - ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Order placed successfully: BUY 75 units
2025-09-14 11:58:17 - main - INFO - ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: 'str' object has no attribute 'get'
2025-09-14 11:59:23 - main - INFO - 🛑 Bot stopped by user
