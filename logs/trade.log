2025-09-14 11:19:32 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$150.0$145.0$2025-09-14 10:00:00+05:30
2025-09-14 11:19:32 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.0, stop_loss=145.0, entry_price=150.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:32 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Instrument not found for NIFTY'}
2025-09-14 11:19:32 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$158.5$2025-09-14 15:30:00+05:30$profit_target_exit$158.5
2025-09-14 11:19:32 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 30, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=158.5, stop_loss=None, entry_price=None, exit_price=158.5, current_price=None, exit_reason='profit_target_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:32 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:32 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Instrument not found for NIFTY'}
2025-09-14 11:19:32 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$150.0$145.0$2025-09-14 10:00:00+05:30
2025-09-14 11:19:32 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.0, stop_loss=145.0, entry_price=150.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:32 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Instrument not found for NIFTY'}
2025-09-14 11:19:33 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$158.5$2025-09-14 15:30:00+05:30$test_exit$158.5
2025-09-14 11:19:33 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 30, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=158.5, stop_loss=None, entry_price=None, exit_price=158.5, current_price=None, exit_reason='test_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:19:33 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:19:33 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Instrument not found for NIFTY'}
2025-09-14 11:30:22 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$150.0$145.0$2025-09-14 10:00:00+05:30
2025-09-14 11:30:22 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.0, stop_loss=145.0, entry_price=150.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:30:22 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:30:23 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NoneType' object has no attribute 'get'"}
2025-09-14 11:30:23 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$158.5$2025-09-14 15:30:00+05:30$profit_target_exit$158.5
2025-09-14 11:30:23 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 30, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=158.5, stop_loss=None, entry_price=None, exit_price=158.5, current_price=None, exit_reason='profit_target_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:30:23 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:30:24 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Invalid action CLOSE'}
2025-09-14 11:32:41 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$BANKNIFTY 18 SEP 52000 CALL$200.0$190.0$2025-09-14 11:00:00+05:30
2025-09-14 11:32:41 - trade - INFO - Parsed signal: TradeSignal(symbol='BANKNIFTY', action='BUY', strike=52000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 11, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=200.0, stop_loss=190.0, entry_price=200.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:32:41 - trade - INFO - Dataframe updated: Trade entry created for BANKNIFTY 18 SEP 52000 CALL
2025-09-14 11:32:42 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NoneType' object has no attribute 'get'"}
2025-09-14 11:32:42 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$BANKNIFTY 18 SEP 52000 CALL$215.5$2025-09-14 16:00:00+05:30$profit_target_exit$215.5
2025-09-14 11:32:42 - trade - INFO - Parsed signal: TradeSignal(symbol='BANKNIFTY', action='CLOSE', strike=52000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 16, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=215.5, stop_loss=None, entry_price=None, exit_price=215.5, current_price=None, exit_reason='profit_target_exit', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:32:42 - trade - INFO - Dataframe updated: Trade completed and logged for BANKNIFTY 18 SEP 52000 CALL
2025-09-14 11:32:43 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Invalid action CLOSE'}
