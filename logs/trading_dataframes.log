2025-09-14 11:04:19 - trading_dataframes - INFO - Created log directory: trade_logs
2025-09-14 11:04:19 - trading_dataframes - INFO - Trading Dataframe Manager initialized
2025-09-14 11:05:15 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:05:15 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:05:44 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:05:44 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at price 154.65
2025-09-14 11:05:44 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:05:44 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:19:32 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:19:32 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:19:32 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:19:32 - trading_dataframes - WARNING - Instrument not found for NIFTY
2025-09-14 11:19:32 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 150.0
2025-09-14 11:19:32 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:19:32 - trading_dataframes - WARNING - Instrument not found for NIFTY
2025-09-14 11:19:32 - trading_dataframes - WARNING - Could not calculate actual P&L due to missing market prices
2025-09-14 11:19:32 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 158.5, actual price None
2025-09-14 11:19:32 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:19:32 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:19:32 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:19:32 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:19:32 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:19:32 - trading_dataframes - WARNING - Instrument not found for NIFTY
2025-09-14 11:19:32 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 150.0
2025-09-14 11:19:33 - trading_dataframes - WARNING - Instrument not found for NIFTY
2025-09-14 11:19:33 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:19:33 - trading_dataframes - WARNING - Instrument not found for NIFTY
2025-09-14 11:19:33 - trading_dataframes - WARNING - Could not calculate actual P&L due to missing market prices
2025-09-14 11:19:33 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 158.5, actual price None
2025-09-14 11:19:33 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:19:33 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:22:26 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:22:26 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:22:26 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:25:21 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:25:21 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:25:21 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:30:22 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:30:22 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:30:22 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:30:22 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:30:22 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:30:22 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:30:22 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 150.0
2025-09-14 11:30:23 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:30:23 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:30:23 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:30:23 - trading_dataframes - INFO - Actual P&L calculated: 8.5 (Entry: 150.0, Exit: 158.5)
2025-09-14 11:30:23 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 158.5, actual price 158.5
2025-09-14 11:30:23 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:30:23 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:32:41 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:32:41 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:32:41 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:32:41 - trading_dataframes - INFO - Processing signal: TRADE - BUY for BANKNIFTY
2025-09-14 11:32:41 - trading_dataframes - WARNING - Could not retrieve market price for BANKNIFTY
2025-09-14 11:32:41 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:32:41 - trading_dataframes - INFO - Created new trade entry for BANKNIFTY 18 SEP 52000 CALL at price 200.0
2025-09-14 11:32:42 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for BANKNIFTY
2025-09-14 11:32:42 - trading_dataframes - WARNING - Could not retrieve market price for BANKNIFTY
2025-09-14 11:32:42 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:32:42 - trading_dataframes - INFO - Actual P&L calculated: 15.5 (Entry: 200.0, Exit: 215.5)
2025-09-14 11:32:42 - trading_dataframes - INFO - Updated trade exit for BANKNIFTY 18 SEP 52000 CALL at signal price 215.5, actual price 215.5
2025-09-14 11:32:42 - trading_dataframes - INFO - Logged completed trade for BANKNIFTY 18 SEP 52000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:32:42 - trading_dataframes - INFO - Cleaned up completed trade for BANKNIFTY 18 SEP 52000 CALL from active memory
2025-09-14 11:34:10 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:34:10 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:34:10 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:34:22 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:34:22 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:34:22 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:36:19 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:36:19 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:36:19 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:36:31 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:36:31 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:36:31 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:36:31 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:36:37 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:36:37 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:36:37 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:36:37 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:36:37 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:36:37 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:36:37 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:44:14 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:44:14 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:44:14 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:44:35 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:44:36 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:44:36 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:44:36 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:45:07 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:45:07 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:45:07 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:45:07 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:45:07 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:45:07 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:45:07 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:46:46 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:46:47 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:46:47 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:47:27 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:47:28 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:47:28 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:47:28 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:47:37 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:47:38 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:47:38 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:47:38 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:47:38 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:47:38 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:47:38 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:50:05 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:50:05 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:50:05 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:50:16 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:50:17 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:50:17 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:50:17 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:50:21 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:50:22 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:50:22 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:50:22 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:50:22 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:50:22 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:50:22 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:55:05 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 11:55:05 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 11:55:05 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 11:55:41 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:55:42 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:55:42 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:55:42 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:56:21 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:56:22 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:56:22 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:56:22 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:56:22 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:56:22 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:56:22 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
2025-09-14 11:58:10 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 11:58:11 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:58:11 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:58:11 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 155.4
2025-09-14 11:58:16 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 11:58:17 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 11:58:17 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 11:58:17 - trading_dataframes - INFO - Actual P&L calculated: -0.75 (Entry: 155.4, Exit: 154.65)
2025-09-14 11:58:17 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 CALL at signal price 154.65, actual price 154.65
2025-09-14 11:58:17 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 CALL to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 11:58:17 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 CALL from active memory
